import logo from './logo.svg';
import './App.css';
import BioData from './components/BioData';
import {useState} from 'react';
function App() {
  const [noteTitle, setNoteTitle] = useState("xxxx");
  const [notes, setNotes] = useState([
    {id:1, title:"Note 1"},
    {id:2, title:"Note 2"}
  ]);


  const handleNoteTitle = (e) => {
    setNoteTitle(e.target.value);
  }

  const submitHandler = (e) => {
    e.preventDefault();
    if (noteTitle.trim() === "") {
      return alert("Please enter a note title");
    }
    const newNote = {
      id: Date.now() + "", 
      title: noteTitle
    };
    setNotes([...notes, newNote]);
    setNoteTitle("");
  };
  const removeHandler = (noteId) => {
    const updatedNotes = notes.filter((note) => note.id !== noteId);
    setNotes(updatedNotes);
  };
  
  return ( 
    <div className="App">
      <form onSubmit={submitHandler}>
        <input type="text" value={noteTitle} onChange={handleNoteTitle}/>
        <button type="submit">Add</button>
      </form>
      <div className="notes">
        <h2>All Notes</h2>
        <ul>
          {
            notes.map((note) => (
              <li key={note.id}><span>{note.title}</span>              <button>Edit</button>
              <button onClick= {removeHandler}>Delete</button> </li>

            ))
          }
        </ul>
      </div>
    </div>
  );
}

export default App;
